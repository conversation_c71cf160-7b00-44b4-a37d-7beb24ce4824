<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <groupId>com.xcs.spring</groupId>
    <artifactId>spring-reading</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>spring-reading</name>
    <description>spring-reading</description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <java.version>11</java.version>
        <spring.version>5.2.15.RELEASE</spring.version>
        <mysql.version>8.0.30</mysql.version>
        <spring.boot.version>2.3.12.RELEASE</spring.boot.version>
    </properties>

    <modules>
        <module>spring-annotation</module>
        <module>spring-aware</module>
        <module>spring-interface</module>
        <module>spring-jsr</module>
        <module>spring-core</module>
        <module>spring-aop</module>
        <module>spring-mvc</module>
        <module>spring-resources</module>
        <module>spring-metadata</module>
        <module>spring-beans</module>
        <module>spring-context</module>
        <module>spring-factory</module>
        <module>spring-env</module>
        <module>spring-dataops</module>
        <module>spring-spel</module>
        <module>spring-transaction</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
    </dependencies>

</project>
