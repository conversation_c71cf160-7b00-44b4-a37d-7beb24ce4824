<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-reading</artifactId>
        <groupId>com.xcs.spring</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-jsr</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>spring-jsr330-inject</module>
        <module>spring-jsr330-qualifier</module>
        <module>spring-jsr330-named</module>
        <module>spring-jsr330-singleton</module>
        <module>spring-jsr330-scope</module>
        <module>spring-jsr330-provider</module>
        <module>spring-jsr250-resource</module>
        <module>spring-jsr250-postConstruct</module>
        <module>spring-jsr250-preDestroy</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>1.3.5</version>
        </dependency>
    </dependencies>

</project>