<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xcs.spring</groupId>
        <artifactId>spring-aop</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <packaging>jar</packaging>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-aop-enableLoadTimeWeaving</artifactId>

    <properties>
        <aspectj.version>1.9.7</aspectj.version>
        <spring-boot.version>2.3.5.RELEASE</spring-boot.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-instrument</artifactId>
            <version>${spring.version}</version>
        </dependency>
    </dependencies>
</project>