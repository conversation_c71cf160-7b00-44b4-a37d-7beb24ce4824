# 07-Web-Framework (Web框架篇)

## 概述

Web框架篇介绍Spring MVC框架，这是Spring生态系统中用于构建Web应用程序的核心模块。Spring MVC基于Model-View-Controller设计模式，提供了灵活、强大的Web开发解决方案。

## 学习目标

- 理解MVC设计模式和Spring MVC架构
- 掌握Spring MVC的核心组件和工作流程
- 学会配置和使用Spring MVC
- 熟悉RESTful Web服务开发
- 理解Spring MVC的扩展机制

## 学习路径

### 📁 01-spring-mvc (Spring MVC)

#### 🌐 mvc-basics (MVC基础)
- **Spring MVC核心概念** - 理解MVC模式在Web开发中的应用
- **DispatcherServlet** - Spring MVC的前端控制器，负责请求分发
- **HandlerMapping** - 请求映射处理器，将请求映射到处理器
- **Controller** - 控制器组件，处理业务逻辑
- **ViewResolver** - 视图解析器，解析逻辑视图名到具体视图

#### ⚙️ web-configuration (Web配置)
- **WebMvcConfigurer** - Spring MVC配置接口
- **@EnableWebMvc** - 启用Spring MVC配置
- **静态资源处理** - 配置静态资源访问
- **拦截器配置** - 配置请求拦截器
- **异常处理配置** - 全局异常处理机制

## Spring MVC架构

### 核心组件

```
Client Request
    ↓
DispatcherServlet (前端控制器)
    ↓
HandlerMapping (处理器映射)
    ↓
HandlerAdapter (处理器适配器)
    ↓
Controller (控制器)
    ↓
ModelAndView (模型和视图)
    ↓
ViewResolver (视图解析器)
    ↓
View (视图)
    ↓
Response to Client
```

### 请求处理流程

1. **请求接收** - DispatcherServlet接收HTTP请求
2. **处理器映射** - HandlerMapping查找对应的处理器
3. **处理器适配** - HandlerAdapter适配处理器
4. **业务处理** - Controller执行业务逻辑
5. **视图解析** - ViewResolver解析视图
6. **视图渲染** - View渲染响应内容
7. **响应返回** - 返回HTTP响应给客户端

## 核心注解

### 控制器注解

| 注解 | 说明 | 示例 |
|------|------|------|
| @Controller | 标记控制器类 | @Controller |
| @RestController | RESTful控制器 | @RestController |
| @RequestMapping | 请求映射 | @RequestMapping("/users") |
| @GetMapping | GET请求映射 | @GetMapping("/{id}") |
| @PostMapping | POST请求映射 | @PostMapping |
| @PutMapping | PUT请求映射 | @PutMapping("/{id}") |
| @DeleteMapping | DELETE请求映射 | @DeleteMapping("/{id}") |

### 参数绑定注解

| 注解 | 说明 | 示例 |
|------|------|------|
| @RequestParam | 请求参数绑定 | @RequestParam String name |
| @PathVariable | 路径变量绑定 | @PathVariable Long id |
| @RequestBody | 请求体绑定 | @RequestBody User user |
| @RequestHeader | 请求头绑定 | @RequestHeader String token |
| @CookieValue | Cookie值绑定 | @CookieValue String sessionId |
| @ModelAttribute | 模型属性绑定 | @ModelAttribute User user |

## 使用示例

### 1. 基本控制器
```java
@Controller
@RequestMapping("/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping
    public String listUsers(Model model) {
        model.addAttribute("users", userService.findAll());
        return "users/list";
    }
    
    @GetMapping("/{id}")
    public String showUser(@PathVariable Long id, Model model) {
        model.addAttribute("user", userService.findById(id));
        return "users/show";
    }
    
    @PostMapping
    public String createUser(@ModelAttribute User user) {
        userService.save(user);
        return "redirect:/users";
    }
}
```

### 2. RESTful控制器
```java
@RestController
@RequestMapping("/api/users")
public class UserRestController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping
    public List<User> listUsers() {
        return userService.findAll();
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return user != null ? ResponseEntity.ok(user) : ResponseEntity.notFound().build();
    }
    
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody @Valid User user) {
        User savedUser = userService.save(user);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody @Valid User user) {
        user.setId(id);
        User updatedUser = userService.update(user);
        return ResponseEntity.ok(updatedUser);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
```

### 3. 配置类
```java
@Configuration
@EnableWebMvc
@ComponentScan(basePackages = "com.example.web")
public class WebConfig implements WebMvcConfigurer {
    
    @Override
    public void configureViewResolvers(ViewResolverRegistry registry) {
        registry.jsp("/WEB-INF/views/", ".jsp");
    }
    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**")
                .addResourceLocations("/static/");
    }
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new LoggingInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/static/**");
    }
    
    @Override
    public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> resolvers) {
        resolvers.add(new GlobalExceptionHandler());
    }
}
```

### 4. 全局异常处理
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleValidationException(ValidationException e) {
        ErrorResponse error = new ErrorResponse("VALIDATION_ERROR", e.getMessage());
        return ResponseEntity.badRequest().body(error);
    }
    
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleResourceNotFoundException(ResourceNotFoundException e) {
        ErrorResponse error = new ErrorResponse("RESOURCE_NOT_FOUND", e.getMessage());
        return ResponseEntity.notFound().build();
    }
    
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception e) {
        ErrorResponse error = new ErrorResponse("INTERNAL_ERROR", "An unexpected error occurred");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
```

## 高级特性

### 1. 内容协商
```java
@GetMapping(value = "/users/{id}", produces = {
    MediaType.APPLICATION_JSON_VALUE,
    MediaType.APPLICATION_XML_VALUE
})
public User getUser(@PathVariable Long id) {
    return userService.findById(id);
}
```

### 2. 文件上传
```java
@PostMapping("/upload")
public String handleFileUpload(@RequestParam("file") MultipartFile file) {
    if (!file.isEmpty()) {
        // 处理文件上传逻辑
        fileService.save(file);
    }
    return "redirect:/files";
}
```

### 3. 异步处理
```java
@GetMapping("/async")
public DeferredResult<String> asyncMethod() {
    DeferredResult<String> deferredResult = new DeferredResult<>();
    
    // 异步处理
    CompletableFuture.supplyAsync(() -> {
        // 长时间运行的任务
        return "Async result";
    }).whenComplete((result, throwable) -> {
        if (throwable != null) {
            deferredResult.setErrorResult(throwable);
        } else {
            deferredResult.setResult(result);
        }
    });
    
    return deferredResult;
}
```

### 4. WebSocket支持
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new MyWebSocketHandler(), "/websocket")
                .setAllowedOrigins("*");
    }
}
```

## 测试支持

### 1. 控制器测试
```java
@WebMvcTest(UserController.class)
public class UserControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    public void testGetUser() throws Exception {
        User user = new User(1L, "John Doe");
        when(userService.findById(1L)).thenReturn(user);
        
        mockMvc.perform(get("/users/1"))
                .andExpect(status().isOk())
                .andExpect(model().attribute("user", user))
                .andExpect(view().name("users/show"));
    }
}
```

### 2. REST API测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
public class UserRestControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testCreateUser() {
        User user = new User("John Doe", "<EMAIL>");
        
        ResponseEntity<User> response = restTemplate.postForEntity("/api/users", user, User.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getName()).isEqualTo("John Doe");
    }
}
```

## 性能优化

### 1. 缓存配置
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager("users", "products");
    }
}
```

### 2. 压缩配置
```java
@Configuration
public class CompressionConfig {
    
    @Bean
    public FilterRegistrationBean<GzipFilter> gzipFilter() {
        FilterRegistrationBean<GzipFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new GzipFilter());
        registration.addUrlPatterns("/*");
        return registration;
    }
}
```

## 前置知识

- [06-Transaction-Management (事务管理篇)](../06-Transaction-Management/) - 事务管理
- HTTP协议基础
- Servlet API基础
- MVC设计模式
- RESTful架构风格

## 扩展学习

完成Web框架篇学习后，可以继续学习：
- Spring Boot - 简化Spring应用开发
- Spring Security - Web安全框架
- Spring WebFlux - 响应式Web框架
- Spring Cloud - 微服务架构
