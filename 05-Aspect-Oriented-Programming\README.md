# 05-Aspect-Oriented-Programming (AOP篇)

## 概述

面向切面编程(AOP)篇深入探讨Spring AOP的实现原理和使用方法。AOP是Spring框架的核心特性之一，用于处理横切关注点，如日志记录、事务管理、安全检查等。

## 学习目标

- 理解AOP的核心概念和术语
- 掌握JDK动态代理和CGLIB代理的原理
- 熟悉切点、通知、切面的定义和使用
- 学会Spring AOP的基础设施和扩展机制
- 理解AspectJ与Spring AOP的集成

## 学习路径

### 📁 01-proxy-fundamentals (代理基础)

#### 🔧 jdk-dynamic-proxy (JDK动态代理)
- **[JdkProxy](proxy-fundamentals/jdk-dynamic-proxy/jdk-proxy/)** - 接口实现，动态生成代理类，处理方法调用，统一横切关注点

#### 🛠️ cglib-proxy (CGLIB代理)
- **[CglibProxy](proxy-fundamentals/cglib-proxy/cglib-proxy/)** - 基于字节码生成的库，无需接口，可拦截类方法并进行增强

### 📁 02-aop-concepts (AOP概念)

#### 🎯 pointcuts-and-advice (切点与通知)
- **[ClassFilter](aop-concepts/pointcuts-and-advice/class-filter/)** - 确定类是否匹配拦截条件
- **[MethodMatcher](aop-concepts/pointcuts-and-advice/method-matcher/)** - 确定方法是否匹配拦截条件
- **[Pointcut](aop-concepts/pointcuts-and-advice/pointcut/)** - 定义切入点，匹配被拦截的方法
- **[Advice](aop-concepts/pointcuts-and-advice/advice/)** - AOP中定义各种通知类型行为的核心接口
- **[MethodInterceptor](aop-concepts/pointcuts-and-advice/method-interceptor/)** - 拦截方法执行，允许在前后添加额外逻辑
- **[MethodBeforeAdvice](aop-concepts/pointcuts-and-advice/method-before-advice/)** - 允许在方法调用之前插入自定义逻辑
- **[AfterReturningAdvice](aop-concepts/pointcuts-and-advice/after-returning-advice/)** - 允许在方法调用之后插入自定义逻辑
- **[ThrowsAdvice](aop-concepts/pointcuts-and-advice/throws-advice/)** - 异常通知，捕获方法抛出的异常，执行额外逻辑
- **[IntroductionInterceptor](aop-concepts/pointcuts-and-advice/introduction-interceptor/)** - 动态地向目标对象引入新的功能或属性

#### 🎭 advisors-and-aspects (通知器与切面)
- **[Advisor](aop-concepts/advisors-and-aspects/advisor/)** - 用于将通知和切点结合，实现切面编程的横切关注点
- **[Advised](aop-concepts/advisors-and-aspects/advised/)** - 配置AOP代理的通知、通知器、目标等

### 📁 03-aop-infrastructure (AOP基础设施)

#### 🏭 proxy-creation (代理创建)
- **[ProxyFactory](aop-infrastructure/proxy-creation/proxy-factory/)** - 一种便捷的方式来创建代理对象
- **[AopProxyFactory](aop-infrastructure/proxy-creation/aop-proxy-factory/)** - 创建AOP代理工厂，支持JDK和CGLIB
- **[AopProxy](aop-infrastructure/proxy-creation/aop-proxy/)** - 创建和管理AOP代理对象
- **[AdvisorChainFactory](aop-infrastructure/proxy-creation/advisor-chain-factory/)** - 创建Advisor链的工厂接口
- **[AdvisorAdapterRegistry](aop-infrastructure/proxy-creation/advisor-adapter-registry/)** - 适配各种Advice到AOP拦截器，注册和管理Advisor适配器
- **[AdvisorAdapter](aop-infrastructure/proxy-creation/advisor-adapter/)** - 适配不同类型通知到拦截器链
- **[ProxyMethodInvocation](aop-infrastructure/proxy-creation/proxy-method-invocation/)** - AOP方法调用代理，处理拦截器链和方法调用

#### 🔧 aspectj-integration (AspectJ集成)
- **[@EnableAspectJAutoProxy](aop-infrastructure/aspectj-integration/enable-aspectj-auto-proxy/)** - 启用AspectJ切面自动代理
- **[AnnotationAwareAspectJAutoProxyCreator](aop-infrastructure/aspectj-integration/annotation-aware-aspectj-auto-proxy-creator/)** - 创建AOP代理以应用AspectJ风格的切面
- **[BeanFactoryAdvisorRetrievalHelper](aop-infrastructure/aspectj-integration/bean-factory-advisor-retrieval-helper/)** - 帮助检索并管理Spring AOP中的Advisor Beans
- **[BeanFactoryAspectJAdvisorsBuilder](aop-infrastructure/aspectj-integration/bean-factory-aspectj-advisors-builder/)** - 构建@AspectJ注解切面，生成Spring AOP Advisors
- **[AspectInstanceFactory](aop-infrastructure/aspectj-integration/aspect-instance-factory/)** - 创建切面实例，支持多种实现方式
- **[MetadataAwareAspectInstanceFactory](aop-infrastructure/aspectj-integration/metadata-aware-aspect-instance-factory/)** - 管理切面实例和元数据，支持多种实例化策略
- **[AspectJAdvisorFactory](aop-infrastructure/aspectj-integration/aspectj-advisor-factory/)** - 创建AspectJ通知器实例，管理切面通知的创建和配置
- **[TargetSource](aop-infrastructure/aspectj-integration/target-source/)** - 管理AOP代理对象的获取与释放
- **[TargetSourceCreator](aop-infrastructure/aspectj-integration/target-source-creator/)** - 创建特殊的目标源，定制代理对象的创建和管理
- **[AopContext](aop-infrastructure/aspectj-integration/aop-context/)** - 获取Spring AOP代理对象的工具
- **[ExposeInvocationInterceptor](aop-infrastructure/aspectj-integration/expose-invocation-interceptor/)** - 暴露Spring AOP方法调用上下文的拦截器
- **[@EnableLoadTimeWeaving](aop-infrastructure/aspectj-integration/enable-load-time-weaving/)** - 启用Spring加载时编织

## AOP核心概念

### 基本术语

| 术语 | 英文 | 说明 |
|------|------|------|
| 切面 | Aspect | 横切关注点的模块化 |
| 连接点 | Join Point | 程序执行过程中的特定点 |
| 切点 | Pointcut | 匹配连接点的表达式 |
| 通知 | Advice | 在切点执行的代码 |
| 目标对象 | Target Object | 被代理的原始对象 |
| 代理对象 | Proxy Object | AOP框架创建的对象 |
| 织入 | Weaving | 将切面应用到目标对象的过程 |

### 通知类型

1. **前置通知 (Before Advice)** - 在方法执行前执行
2. **后置通知 (After Returning Advice)** - 在方法正常返回后执行
3. **异常通知 (After Throwing Advice)** - 在方法抛出异常后执行
4. **最终通知 (After Finally Advice)** - 无论方法如何结束都会执行
5. **环绕通知 (Around Advice)** - 包围方法执行，最强大的通知类型

### 代理方式选择

```
目标对象是否实现接口？
    ├─ 是 → JDK动态代理
    └─ 否 → CGLIB代理
```

## AOP实现原理

### 1. 代理对象创建流程
```
1. 解析切面配置
2. 创建Advisor链
3. 选择代理方式(JDK/CGLIB)
4. 创建代理对象
5. 返回代理对象
```

### 2. 方法调用拦截流程
```
1. 代理对象方法调用
2. 获取拦截器链
3. 创建方法调用对象
4. 执行拦截器链
5. 调用目标方法
6. 返回结果
```

### 3. 切点匹配机制
```
1. ClassFilter匹配类
2. MethodMatcher匹配方法
3. 组合形成Pointcut
4. Advisor包装Pointcut和Advice
```

## 使用场景

### 1. 日志记录
```java
@Aspect
@Component
public class LoggingAspect {
    @Around("@annotation(Loggable)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long end = System.currentTimeMillis();
        System.out.println("Method executed in " + (end - start) + "ms");
        return result;
    }
}
```

### 2. 事务管理
```java
@Transactional
@Service
public class UserService {
    public void createUser(User user) {
        // 业务逻辑
    }
}
```

### 3. 安全检查
```java
@Aspect
@Component
public class SecurityAspect {
    @Before("@annotation(RequiresRole)")
    public void checkRole(JoinPoint joinPoint) {
        // 权限检查逻辑
    }
}
```

## 性能考虑

### 1. 代理方式性能对比
- **JDK动态代理**：基于接口，性能较好
- **CGLIB代理**：基于继承，创建开销较大，但运行时性能接近

### 2. 切点表达式优化
- 使用具体的包名和类名
- 避免使用过于宽泛的表达式
- 合理使用缓存机制

## 前置知识

- [04-Extension-Points (扩展点篇)](../04-Extension-Points/) - 后置处理器机制
- Java动态代理机制
- 设计模式：代理模式、装饰器模式
- AspectJ基础语法

## 下一步

完成AOP篇学习后，建议继续学习：
- [06-Transaction-Management (事务管理篇)](../06-Transaction-Management/) - 声明式事务管理
