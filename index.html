<table align="center">
    <tr align="center">
        <th>标题</th>
        <th>地址</th>
        <th>难度级别</th>
        <th>视频讲解</th>
    </tr>
    <tr align="center">
        <td><strong>【资源加载与访问】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>资源加载</td>
        <td><a href="spring-resources/spring-resource/README.md">Resource</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>资源加载器</td>
        <td><a href="spring-resources/spring-resource-resourceLoader/README.md">ResourceLoader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>XML资源加载器</td>
        <td><a href="spring-resources/spring-resource-documentLoader/README.md">DocumentLoader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【元数据与过滤】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>类元数据读取</td>
        <td><a href="spring-metadata/spring-metadata-metadataReader/README.md">MetadataReader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>注解元数据</td>
        <td><a href="spring-metadata/spring-metadata-annotationMetadata/README.md">AnnotationMetadata</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>类过滤器</td>
        <td><a href="spring-metadata/spring-metadata-typeFilter/README.md">TypeFilter</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>条件过滤器</td>
        <td><a href="spring-metadata/spring-metadata-condition/README.md">Condition</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【Bean定义与注册】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>Bean定义</td>
        <td><a href="spring-beans/spring-bean-beanDefinition/README.md">BeanDefinition</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean定义持有者</td>
        <td><a href="spring-beans/spring-bean-beanDefinitionHolder/README.md">BeanDefinitionHolder</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean定义注册器</td>
        <td><a href="spring-beans/spring-bean-beanDefinitionRegistry/README.md">BeanDefinitionRegistry</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【Bean定义读取与扫描】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>XML Bean定义读取器</td>
        <td><a href="spring-beans/spring-bean-xmlBeanDefinitionReader/README.md">XmlBeanDefinitionReader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>属性文件Bean定义读取器</td>
        <td><a href="spring-beans/spring-bean-propertiesBeanDefinitionReader/README.md">PropertiesBeanDefinitionReader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Groovy脚本Bean定义读取器</td>
        <td><a href="spring-beans/spring-bean-groovyBeanDefinitionReader/README.md">GroovyBeanDefinitionReader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E7%AE%80%E5%8D%95-Green"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>注解Bean定义读取器</td>
        <td><a href="spring-beans/spring-bean-annotatedBeanDefinitionReader/README.md">AnnotatedBeanDefinitionReader</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>类路径Bean定义扫描器</td>
        <td><a href="spring-beans/spring-bean-classPathBeanDefinitionScanner/README.md">ClassPathBeanDefinitionScanner</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【Bean生命周期过程】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>Bean的定义解析</td>
        <td><a href="#">Bean的定义解析</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean的初始化过程</td>
        <td><a href="spring-core/spring-core-getBean/README.md">Bean的初始化过程</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean的依赖解析过程</td>
        <td><a href="spring-core/spring-core-resolveDependency/README.md">Bean的依赖解析过程</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean的销毁过程</td>
        <td><a href="#">Bean的销毁过程</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【后置处理器与初始化】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>属性设置后的初始化操作</td>
        <td><a href="spring-interface/spring-interface-initializingBean/README.md">InitializingBean</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>资源清理与销毁</td>
        <td><a href="spring-interface/spring-interface-disposableBean/README.md">DisposableBean</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>动态修改Bean定义</td>
        <td><a href="spring-interface/spring-interface-beanDefinitionRegistryPostProcessor/README.md">BeanDefinitionRegistryPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>动态调整Bean配置</td>
        <td><a href="spring-interface/spring-interface-beanFactoryPostProcessor/README.md">BeanFactoryPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>调整Bean属性</td>
        <td><a href="spring-interface/spring-interface-beanPostProcessor/README.md">BeanPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean实例拦截</td>
        <td><a href="spring-interface/spring-interface-instantiationAwareBeanPostProcessor/README.md">InstantiationAwareBeanPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean销毁生命周期</td>
        <td><a href="spring-interface/spring-interface-destructionAwareBeanPostProcessor/README.md">DestructionAwareBeanPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean定义的动态处理</td>
        <td><a href="spring-interface/spring-interface-mergedBeanDefinitionPostProcessor/README.md">MergedBeanDefinitionPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>调整Bean实例化策略</td>
        <td><a href="spring-interface/spring-interface-smartInstantiationAwareBeanPostProcessor/README.md">SmartInstantiationAwareBeanPostProcessor</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>All Beans完全初始化后</td>
        <td><a href="spring-interface/spring-interface-smartInitializingSingleton/README.md">SmartInitializingSingleton</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【Aware接口】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>获取Bean名称</td>
        <td><a href="spring-aware/spring-aware-beanNameAware/README.md">BeanNameAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>获取类加载器</td>
        <td><a href="spring-aware/spring-aware-beanClassLoaderAware/README.md">BeanClassLoaderAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>与Bean工厂互动</td>
        <td><a href="spring-aware/spring-aware-beanFactoryAware/README.md">BeanFactoryAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>感知运行环境</td>
        <td><a href="spring-aware/spring-aware-environmentAware/README.md">EnvironmentAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>嵌入值解析</td>
        <td><a href="spring-aware/spring-aware-embeddedValueResolverAware/README.md">EmbeddedValueResolverAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>资源加载策略</td>
        <td><a href="spring-aware/spring-aware-resourceLoaderAware/README.md">ResourceLoaderAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>发布应用事件</td>
        <td><a href="spring-aware/spring-aware-applicationEventPublisherAware/README.md">ApplicationEventPublisherAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>访问消息源</td>
        <td><a href="spring-aware/spring-aware-messageSourceAware/README.md">MessageSourceAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>感知应用启动过程</td>
        <td><a href="spring-aware/spring-aware-applicationStartupAware/README.md">ApplicationStartupAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>访问应用上下文</td>
        <td><a href="spring-aware/spring-aware-applicationContextAware/README.md">ApplicationContextAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>了解关联导入信息</td>
        <td><a href="spring-aware/spring-aware-importAware/README.md">ImportAware</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E4%B8%80%E8%88%AC-blue"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td><strong>【核心注解】</strong></td>
        <td colspan="3"></td>
    </tr>
    <tr align="center">
        <td>Java配置</td>
        <td><a href="spring-annotation/spring-annotation-configuration/README.md">@Configuration</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>组件扫描</td>
        <td><a href="spring-annotation/spring-annotation-componentScan/README.md">@ComponentScan</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>Bean定义</td>
        <td><a href="spring-annotation/spring-annotation-bean/README.md">@Bean</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>导入配置</td>
        <td><a href="spring-annotation/spring-annotation-import/README.md">@Import</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>属性绑定</td>
        <td><a href="spring-annotation/spring-annotation-propertySource/README.md">@PropertySource</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>初始化顺序</td>
        <td><a href="spring-annotation/spring-annotation-dependsOn/README.md">@DependsOn</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>条件注册</td>
        <td><a href="spring-annotation/spring-annotation-conditional/README.md">@Conditional</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>延迟加载</td>
        <td><a href="spring-annotation/spring-annotation-lazy/README.md">@Lazy</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>属性注入</td>
        <td><a href="spring-annotation/spring-annotation-value/README.md">@Value</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>依赖注入</td>
        <td><a href="spring-annotation/spring-annotation-autowired/README.md">@Autowired</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>注入依赖</td>
        <td><a href="spring-jsr/spring-jsr330-inject/README.md">@Inject</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>具名组件</td>
        <td><a href="spring-jsr/spring-jsr330-named/README.md">@Named</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>初始化后操作</td>
        <td><a href="spring-jsr/spring-jsr250-postConstruct/README.md">@PostConstruct</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>销毁前操作</td>
        <td><a href="spring-jsr/spring-jsr250-preDestroy/README.md">@PreDestroy</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>资源绑定</td>
        <td><a href="spring-jsr/spring-jsr250-resource/README.md">@Resource</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>提供者机制</td>
        <td><a href="spring-jsr/spring-jsr330-provider/README.md">Provider</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>限定符</td>
        <td><a href="spring-jsr/spring-jsr330-qualifier/README.md">@Qualifier</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>作用域定义</td>
        <td><a href="spring-jsr/spring-jsr330-scope/README.md">@Scope</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>单例模式</td>
        <td><a href="spring-jsr/spring-jsr330-singleton/README.md">@Singleton</a></td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>定义主要候选项</td>
        <td>@Primary</td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>添加描述信息</td>
        <td>@Description</td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>指定注解角色</td>
        <td>@Role</td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>标记为可索引</td>
        <td>@Indexed</td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
    <tr align="center">
        <td>指定顺序</td>
        <td>@Order</td>
        <td><img src="https://img.shields.io/badge/Level-%E5%9B%B0%E9%9A%BE-orange"/></td>
        <td>❌</td>
    </tr>
</table>