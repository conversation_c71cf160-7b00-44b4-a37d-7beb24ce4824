<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-reading</artifactId>
        <groupId>com.xcs.spring</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-annotation</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>spring-annotation-configuration</module>
        <module>spring-annotation-bean</module>
        <module>spring-annotation-import</module>
        <module>spring-annotation-propertySource</module>
        <module>spring-annotation-componentScan</module>
        <module>spring-annotation-dependsOn</module>
        <module>spring-annotation-lazy</module>
        <module>spring-annotation-conditional</module>
        <module>spring-annotation-value</module>
        <module>spring-annotation-autowired</module>
        <module>spring-annotation-profile</module>
    </modules>

</project>