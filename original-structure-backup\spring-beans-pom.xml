<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-reading</artifactId>
        <groupId>com.xcs.spring</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-beans</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>spring-bean-beanDefinition</module>
        <module>spring-bean-beanDefinitionHolder</module>
        <module>spring-bean-beanDefinitionRegistry</module>
        <module>spring-bean-xmlBeanDefinitionReader</module>
        <module>spring-bean-propertiesBeanDefinitionReader</module>
        <module>spring-bean-groovyBeanDefinitionReader</module>
        <module>spring-bean-annotatedBeanDefinitionReader</module>
        <module>spring-bean-classPathBeanDefinitionScanner</module>
    </modules>

</project>