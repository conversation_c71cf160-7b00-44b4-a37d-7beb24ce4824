<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-reading</artifactId>
        <groupId>com.xcs.spring</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-spel</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>spring-spel-expressionParser</module>
        <module>spring-spel-evaluationContext</module>
        <module>spring-spel-propertyAccessor</module>
        <module>spring-spel-constructorResolver</module>
        <module>spring-spel-methodResolver</module>
        <module>spring-spel-beanResolver</module>
        <module>spring-spel-typeLocator</module>
        <module>spring-spel-typeConverter</module>
        <module>spring-spel-typeComparator</module>
        <module>spring-spel-operatorOverloader</module>
        <module>spring-spel-expression</module>
    </modules>

</project>