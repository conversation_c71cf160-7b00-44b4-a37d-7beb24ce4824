<div align="center">
    <img alt="logo" src="image/banner.png" style="height: 80px">
</div>
<div align="center">
    <h2>深入Spring，从源码开始！</h2>
    <h4>探索Java最受欢迎的框架，理解它的内部机制，带大家从入门到精通。</h4>
    <h3>🎯 已重新组织为学习导向的结构！</h3>
</div>
<p align="center">
    <a href="https://github.com/xuchengsheng/spring-reading/stargazers"><img src="https://img.shields.io/github/stars/xuchengsheng/spring-reading?logo=github&logoColor=%23EF2D5E&label=Stars&labelColor=%23000000&color=%23EF2D5E&cacheSeconds=3600" alt="Stars Badge"/></a>
    <a href="https://github.com/xuchengsheng"><img src="https://img.shields.io/github/followers/xuchengsheng?label=Followers&logo=github&logoColor=%23FC521F&labelColor=%231A2477&color=%23FC521F&cacheSeconds=3600" alt="Follow Badge"></a>
    <a href="https://github.com/xuchengsheng/spring-reading/fork"><img src="https://img.shields.io/github/forks/xuchengsheng/spring-reading?label=Forks&logo=github&logoColor=%23F2BB13&labelColor=%23BE2323&color=%23F2BB13" alt="Fork Badge"></a>
    <a href="https://github.com/xuchengsheng/spring-reading/watchers"><img src="https://img.shields.io/github/watchers/xuchengsheng/spring-reading?label=Watchers&logo=github&logoColor=%23FF4655&labelColor=%234169E1&color=%23FF4655&cacheSeconds=3600" alt="Watchers Badge"></a>
</p>
<p align="center">
    <img src="https://visitor-badge.lithub.cc/badge?page_id=github.com/xuchengsheng&left_text=Visitors" alt="Visitor Badge"/>
    <img src="https://img.shields.io/badge/WeChat-spring_reading-%2307C160?logo=wechat" alt="Wechat Badge"/>
    <a href="https://blog.csdn.net/duzhuang2399"><img src="https://img.shields.io/badge/dynamic/xml?url=https%3A%2F%2Fblog.csdn.net%2Fduzhuang2399&query=%2F%2F*%5B%40id%3D%22userSkin%22%5D%2Fdiv%5B1%5D%2Fdiv%5B2%5D%2Fdiv%5B1%5D%2Fdiv%2Fdiv%5B2%5D%2Fdiv%5B1%5D%2Fdiv%5B1%5D%2Fdiv%5B2%5D%2Fspan&logo=C&logoColor=red&label=CSDN&color=red&cacheSeconds=3600" alt="CSDN Badge"></a>
        <a href="https://hellogithub.com/repository/f43b683fa175499ca3af4e9b1684e88b" target="_blank"><img src="https://api.hellogithub.com/v1/widgets/recommend.svg?rid=f43b683fa175499ca3af4e9b1684e88b&claim_uid=AVv4KeNnZs2Ig3a&theme=small" alt="Featured｜HelloGitHub"/></a>
</p>
<p align="center">
    ⚡ <a href="#技术">技术</a>
    |
    👋 <a href="#简介">简介</a>
    |
    🍵 <a href="#为何做Spring源码分析">Why</a>
    |
    🙏 <a href="#顺手点个星">点个星</a>
    |
    🌱 <a href="#spring-学习路径">学习路径</a>
    |
    💬 <a href="#与我联系">联系我</a>
    |
    ⛵ <a href="#欢迎贡献">贡献</a>
    |
    🔄 <a href="#持续更新中">更新</a>
    |
    💻 <a href="#我的-github-统计">统计</a>
</p>


---

## ⚡技术

<div align="left">
    <img src="https://img.shields.io/badge/Java-1.8%2B-%23437291?logo=openjdk&logoColor=%23437291"/>
    <img src="https://img.shields.io/badge/Spring-5.3.10-%23437291?logo=Spring&logoColor=%236DB33F&color=%236DB33F"/>
    <img src="https://img.shields.io/badge/SpringBoot-2.5.5-%23437291?logo=SpringBoot&logoColor=%236DB33F&color=%236DB33F"/>
    <img src="https://img.shields.io/badge/Maven-3.6.3-%23437291?logo=Apache%20Maven&logoColor=%23C71A36&color=%23C71A36"/>
    <img src="https://img.shields.io/badge/JSR-330-%2366CCFF?logo=OpenJDK&logoColor=%2366CCFF&color=%2366CCFF"/>
    <img src="https://img.shields.io/badge/JSR-250-%23FF9900?logo=OpenJDK&logoColor=%23FF9900&color=%23FF9900"/>
</div>

## 👋简介
大家好呀，我是Lex👨‍💻。我是一名拥有8年经验的Java 后端开发人员👨‍💼，也是一个对 Spring 框架充满热情❤️的程序员。为了帮助那些希望深入了解 Spring 框架的程序员们🧑‍💻，我创建了这个 “Spring 源码阅读系列”📖。通过这个系列，我希望能够与你们共同探索 Spring 的内部工作机制⚙️。如果您有同样的兴趣或问题🤔，请联系我📩！

## 🍵**为何做Spring源码分析**
在我作为框架研发的开发者👨‍🔬的工作中，我经常遇到需要深入理解和调整框架行为的情况🔧。这些工作不只是简单地使用框架的API，更多地是需要对框架的内部工作方式有详细的了解🔍。虽然Github上有关于Spring的简化版本📦，这些对于入门学习确实有很大的帮助✅，但当涉及到真实的项目应用时，与真正的Spring框架还是有很大的差异❌。因此，我开始深入研究Spring的源码，希望能够更透彻地理解其内部的工作机制，以便更好地应用到我的实际工作中🧰。分享我的源码分析📝，也是为了给那些希望真正理解Spring，而不仅仅是使用它的开发者提供一些参考和帮助🙌。

## 🙏顺手点个星
亲爱的朋友们👥，我真的花了很多心思💭去研究和整理这个“Spring 源码阅读系列”📘。如果你觉得这东西还不错👍，或者给你带来了一点点帮助🤗，麻烦点一下星星吧🌟。这真的对我意义重大🎖，每一颗星✨都能让我觉得所有的努力都是值得的💪。我知道这是小事一桩，但你的那一下点击🖱，对我来说就是最好的鼓励🎉。无论如何，都要感谢你抽时间🕰阅读我的内容，真的很感激🙏！

## 🌱Spring 学习路径

> 📚 **全新学习导向结构** - 按照从基础到高级的学习路径重新组织，让Spring学习更加系统化！

### 🎯 学习路径概览

```
01-Foundation (基础篇)
    ↓
02-Core-Container (核心容器篇)
    ↓
03-Configuration (配置篇)
    ↓
04-Extension-Points (扩展点篇)
    ↓
05-Aspect-Oriented-Programming (AOP篇)
    ↓
06-Transaction-Management (事务管理篇)
    ↓
07-Web-Framework (Web框架篇)
```

---

### 📖 [01-Foundation (基础篇)](01-Foundation/)
**🎯 学习目标**: 掌握Spring框架的基础概念和核心组件

- **📁 资源与元数据** - 资源加载、元数据处理、类型过滤
- **📁 环境与属性** - 属性源管理、环境配置
- **📁 数据操作** - 验证转换、SpEL表达式

<img src="https://img.shields.io/badge/难度-基础-green"> <img src="https://img.shields.io/badge/章节-3个-blue"> <img src="https://img.shields.io/badge/主题-25个-orange">

---

### 🏗️ [02-Core-Container (核心容器篇)](02-Core-Container/)
**🎯 学习目标**: 深入理解IoC容器和依赖注入机制

- **📁 Bean定义** - Bean定义基础、读取器与扫描器
- **📁 Bean工厂** - 工厂接口层次结构
- **📁 应用上下文** - 上下文实现和生命周期
- **📁 Bean生命周期** - 注册、初始化、依赖解析、销毁

<img src="https://img.shields.io/badge/难度-中等-yellow"> <img src="https://img.shields.io/badge/章节-4个-blue"> <img src="https://img.shields.io/badge/主题-20个-orange">

---

### ⚙️ [03-Configuration (配置篇)](03-Configuration/)
**🎯 学习目标**: 掌握Spring的各种配置方式

- **📁 基于注解** - 核心注解、组件扫描
- **📁 JSR标准** - JSR-330依赖注入、JSR-250生命周期

<img src="https://img.shields.io/badge/难度-中等-yellow"> <img src="https://img.shields.io/badge/章节-2个-blue"> <img src="https://img.shields.io/badge/主题-20个-orange">

---

### 🔧 [04-Extension-Points (扩展点篇)](04-Extension-Points/)
**🎯 学习目标**: 学会使用Spring的扩展机制

- **📁 Aware接口** - 上下文感知、资源感知
- **📁 生命周期接口** - 初始化接口、后置处理器

<img src="https://img.shields.io/badge/难度-中等-yellow"> <img src="https://img.shields.io/badge/章节-2个-blue"> <img src="https://img.shields.io/badge/主题-21个-orange">

---

### 🎭 [05-Aspect-Oriented-Programming (AOP篇)](05-Aspect-Oriented-Programming/)
**🎯 学习目标**: 掌握面向切面编程和代理机制

- **📁 代理基础** - JDK动态代理、CGLIB代理
- **📁 AOP概念** - 切点通知、通知器切面
- **📁 AOP基础设施** - 代理创建、AspectJ集成

<img src="https://img.shields.io/badge/难度-困难-red"> <img src="https://img.shields.io/badge/章节-3个-blue"> <img src="https://img.shields.io/badge/主题-30个-orange">

---

### 💳 [06-Transaction-Management (事务管理篇)](06-Transaction-Management/)
**🎯 学习目标**: 理解Spring事务管理机制

- **📁 事务基础** - JDBC基础、事务定义
- **📁 事务管理** - 平台管理器、声明式事务

<img src="https://img.shields.io/badge/难度-中等-yellow"> <img src="https://img.shields.io/badge/章节-2个-blue"> <img src="https://img.shields.io/badge/主题-11个-orange">

---

### 🌐 [07-Web-Framework (Web框架篇)](07-Web-Framework/)
**🎯 学习目标**: 掌握Spring MVC Web开发

- **📁 Spring MVC** - MVC基础、Web配置

<img src="https://img.shields.io/badge/难度-中等-yellow"> <img src="https://img.shields.io/badge/章节-1个-blue"> <img src="https://img.shields.io/badge/主题-扩展中-orange">

---

### 📊 学习统计

| 篇章 | 主题数量 | 难度等级 | 预计学时 |
|------|----------|----------|----------|
| 基础篇 | 25个 | 🟢 基础 | 15小时 |
| 核心容器篇 | 20个 | 🟡 中等 | 25小时 |
| 配置篇 | 20个 | 🟡 中等 | 15小时 |
| 扩展点篇 | 21个 | 🟡 中等 | 20小时 |
| AOP篇 | 30个 | 🔴 困难 | 30小时 |
| 事务管理篇 | 11个 | 🟡 中等 | 15小时 |
| Web框架篇 | 扩展中 | 🟡 中等 | 10小时 |
| **总计** | **127+个** | - | **130+小时** |

---

## 🚀 快速开始

### 推荐学习路径

1. **🔰 新手入门** - 从 [01-Foundation (基础篇)](01-Foundation/) 开始
2. **🏗️ 核心理解** - 学习 [02-Core-Container (核心容器篇)](02-Core-Container/)
3. **⚙️ 配置掌握** - 掌握 [03-Configuration (配置篇)](03-Configuration/)
4. **🔧 扩展学习** - 了解 [04-Extension-Points (扩展点篇)](04-Extension-Points/)
5. **🎭 高级特性** - 深入 [05-Aspect-Oriented-Programming (AOP篇)](05-Aspect-Oriented-Programming/)
6. **💳 事务管理** - 学习 [06-Transaction-Management (事务管理篇)](06-Transaction-Management/)
7. **🌐 Web开发** - 掌握 [07-Web-Framework (Web框架篇)](07-Web-Framework/)

### 学习建议

- 📖 **循序渐进**: 按照推荐路径学习，每个篇章都有明确的前置知识要求
- 🛠️ **动手实践**: 每个主题都包含实际代码示例，建议跟着练习
- 🔗 **关联学习**: 注意各个主题之间的关联关系，构建知识网络
- 📝 **总结归纳**: 学完每个篇章后，总结核心概念和应用场景

### 原始结构参考

如果需要查看原始的模块化结构，可以参考 [original-structure-backup](original-structure-backup/) 目录中保存的原始配置文件。

## 💬与我联系

✉️ [Email](<EMAIL>) | 💬 [Issue](https://github.com/xuchengsheng/spring-reading/issues) | 🌐 [CSDN](https://blog.csdn.net/duzhuang2399?type=blog)  Me about everything!

## ⛵欢迎贡献！

如果你发现任何错误🔍或者有改进建议🛠️，欢迎提交 issue 或者 pull request。你的反馈📢对于我非常宝贵💎！

## 🔄持续更新中

为了给大家提供最新🌱、最有价值的内容💼，我会坚持每天更新这个仓库⏳。每一天，你都可以期待看到一些新的内容或者对已有内容的改进✨。如果你有任何建议或反馈📣，欢迎随时联系我📞。我非常珍视每一个反馈💌，因为这是我持续改进的动力🚀。

## ✨Star History

 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=xuchengsheng/spring-reading&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=xuchengsheng/spring-reading&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=xuchengsheng/spring-reading&type=Date" />
 </picture>

## 🎉Stargazers

[![Stargazers123 repo roster for @xuchengsheng/spring-reading](https://reporoster.com/stars/xuchengsheng/spring-reading)](https://github.com/xuchengsheng/spring-reading/stargazers)

## 🎉Forkers

[![Forkers repo roster for @xuchengsheng/spring-reading](https://reporoster.com/forks/xuchengsheng/spring-reading)](https://github.com/xuchengsheng/spring-reading/network/members)

## 🍱请我吃盒饭？

作者晚上还要写博客✍️,平时还需要工作💼,如果帮到了你可以请作者吃个盒饭🥡
<div>
<img alt="logo" src="image/WeChatPay.png" style="width: 260px;height: 280px">
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<img alt="logo" src="image/Alipay.png" style="width: 260px;height: 280px">
</div>

## 👥**关注公众号**

关注后，回复关键字 **“加群”**，即可加入我们的技术交流群，与更多开发者一起交流学习。

<div>
<img alt="logo" src="image/wechat-mp.png" style="height: 220px">
</div>
