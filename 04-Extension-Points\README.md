# 04-Extension-Points (扩展点篇)

## 概述

扩展点篇介绍了Spring框架提供的各种扩展机制，包括Aware接口系列和生命周期接口。这些扩展点允许开发者在Spring容器的不同阶段插入自定义逻辑。

## 学习目标

- 理解Aware接口的作用和使用场景
- 掌握Bean生命周期中的各种扩展点
- 学会使用后置处理器定制Bean创建过程
- 熟悉Spring容器的扩展机制

## 学习路径

### 📁 01-aware-interfaces (Aware接口)

#### 🏛️ context-aware (上下文感知)
- **[ApplicationContextAware](aware-interfaces/context-aware/application-context-aware/)** - 允许Bean获取应用程序上下文
- **[ApplicationEventPublisherAware](aware-interfaces/context-aware/application-event-publisher-aware/)** - 允许Bean发布应用程序事件
- **[ApplicationStartupAware](aware-interfaces/context-aware/application-startup-aware/)** - 允许Bean获取应用启动信息
- **[MessageSourceAware](aware-interfaces/context-aware/message-source-aware/)** - 允许Bean获取消息源
- **[EnvironmentAware](aware-interfaces/context-aware/environment-aware/)** - 允许Bean获取应用程序环境配置
- **[ImportAware](aware-interfaces/context-aware/import-aware/)** - 允许被导入的配置类获取导入它的类的信息

#### 🔧 resource-aware (资源感知)
- **[BeanNameAware](aware-interfaces/resource-aware/bean-name-aware/)** - 让Bean获取自身在容器中的名字
- **[BeanClassLoaderAware](aware-interfaces/resource-aware/bean-class-loader-aware/)** - 允许Bean获取其类加载器
- **[BeanFactoryAware](aware-interfaces/resource-aware/bean-factory-aware/)** - 提供Bean获取所属的BeanFactory
- **[EmbeddedValueResolverAware](aware-interfaces/resource-aware/embedded-value-resolver-aware/)** - 允许Bean解析嵌入式值占位符
- **[ResourceLoaderAware](aware-interfaces/resource-aware/resource-loader-aware/)** - 允许Bean获取资源加载器

### 📁 02-lifecycle-interfaces (生命周期接口)

#### 🚀 initialization-interfaces (初始化接口)
- **[InitializingBean](lifecycle-interfaces/initialization-interfaces/initializing-bean/)** - 提供Bean初始化时执行自定义逻辑的接口
- **[DisposableBean](lifecycle-interfaces/initialization-interfaces/disposable-bean/)** - 定义Bean销毁前执行清理操作的接口
- **[SmartInitializingSingleton](lifecycle-interfaces/initialization-interfaces/smart-initializing-singleton/)** - 在所有单例Bean初始化完成后，执行自定义逻辑

#### ⚙️ post-processors (后置处理器)
- **[BeanDefinitionRegistryPostProcessor](lifecycle-interfaces/post-processors/bean-definition-registry-post-processor/)** - 在容器启动时，对BeanDefinition动态修改或添加
- **[BeanFactoryPostProcessor](lifecycle-interfaces/post-processors/bean-factory-post-processor/)** - 在Bean实例化前，对BeanFactory进行全局修改或配置
- **[BeanPostProcessor](lifecycle-interfaces/post-processors/bean-post-processor/)** - 在Bean初始化前后，进行自定义处理，可影响所有Bean
- **[InstantiationAwareBeanPostProcessor](lifecycle-interfaces/post-processors/instantiation-aware-bean-post-processor/)** - 提供更深层次的实例化和属性注入控制
- **[DestructionAwareBeanPostProcessor](lifecycle-interfaces/post-processors/destruction-aware-bean-post-processor/)** - 允许在Bean销毁前进行额外的清理操作
- **[MergedBeanDefinitionPostProcessor](lifecycle-interfaces/post-processors/merged-bean-definition-post-processor/)** - 在合并Bean定义时对BeanDefinition进行处理
- **[SmartInstantiationAwareBeanPostProcessor](lifecycle-interfaces/post-processors/smart-instantiation-aware-bean-post-processor/)** - 提供更智能的实例化控制

## 扩展点分类

### 按作用阶段分类

#### 1. 容器启动阶段
- BeanDefinitionRegistryPostProcessor
- BeanFactoryPostProcessor

#### 2. Bean创建阶段
- InstantiationAwareBeanPostProcessor
- SmartInstantiationAwareBeanPostProcessor
- MergedBeanDefinitionPostProcessor

#### 3. Bean初始化阶段
- Aware接口系列
- BeanPostProcessor
- InitializingBean

#### 4. 容器关闭阶段
- DisposableBean
- DestructionAwareBeanPostProcessor

#### 5. 特殊时机
- SmartInitializingSingleton (所有单例Bean初始化完成后)

### 按功能分类

#### 1. 信息获取类 (Aware接口)
- 获取容器信息：ApplicationContextAware、BeanFactoryAware
- 获取Bean信息：BeanNameAware、BeanClassLoaderAware
- 获取环境信息：EnvironmentAware、MessageSourceAware
- 获取资源信息：ResourceLoaderAware、EmbeddedValueResolverAware

#### 2. 生命周期控制类
- 初始化控制：InitializingBean、SmartInitializingSingleton
- 销毁控制：DisposableBean、DestructionAwareBeanPostProcessor

#### 3. 定制处理类 (PostProcessor)
- 容器级别：BeanDefinitionRegistryPostProcessor、BeanFactoryPostProcessor
- Bean级别：BeanPostProcessor、InstantiationAwareBeanPostProcessor

## 使用场景

### 1. 框架集成
```java
@Component
public class MyFrameworkIntegration implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        // 初始化第三方框架
    }
}
```

### 2. 资源管理
```java
@Component
public class ResourceManager implements ResourceLoaderAware, DisposableBean {
    private ResourceLoader resourceLoader;
    
    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }
    
    @Override
    public void destroy() {
        // 清理资源
    }
}
```

### 3. 动态配置
```java
@Component
public class DynamicConfigProcessor implements BeanFactoryPostProcessor {
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {
        // 动态修改Bean定义
    }
}
```

## 执行顺序

### Bean创建过程中的扩展点执行顺序
1. BeanDefinitionRegistryPostProcessor.postProcessBeanDefinitionRegistry()
2. BeanFactoryPostProcessor.postProcessBeanFactory()
3. InstantiationAwareBeanPostProcessor.postProcessBeforeInstantiation()
4. SmartInstantiationAwareBeanPostProcessor.determineCandidateConstructors()
5. MergedBeanDefinitionPostProcessor.postProcessMergedBeanDefinition()
6. InstantiationAwareBeanPostProcessor.postProcessAfterInstantiation()
7. InstantiationAwareBeanPostProcessor.postProcessProperties()
8. Aware接口回调
9. BeanPostProcessor.postProcessBeforeInitialization()
10. InitializingBean.afterPropertiesSet()
11. BeanPostProcessor.postProcessAfterInitialization()
12. SmartInitializingSingleton.afterSingletonsInstantiated()

## 前置知识

- [02-Core-Container (核心容器篇)](../02-Core-Container/) - Bean生命周期
- [03-Configuration (配置篇)](../03-Configuration/) - 注解配置
- Java接口和回调机制

## 下一步

完成扩展点篇学习后，建议继续学习：
- [05-Aspect-Oriented-Programming (AOP篇)](../05-Aspect-Oriented-Programming/) - 面向切面编程
