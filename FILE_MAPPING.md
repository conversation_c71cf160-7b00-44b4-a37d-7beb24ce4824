# File Reorganization Mapping

## 01-Foundation

### 01-resources-and-metadata/resource-loading/
- spring-resources/spring-resource/ → resource/
- spring-resources/spring-resource-resourceLoader/ → resource-loader/
- spring-resources/spring-resource-resourcePatternResolver/ → resource-pattern-resolver/
- spring-resources/spring-resource-documentLoader/ → document-loader/

### 01-resources-and-metadata/metadata-processing/
- spring-metadata/spring-metadata-metadataReader/ → metadata-reader/
- spring-metadata/spring-metadata-annotationMetadata/ → annotation-metadata/
- spring-metadata/spring-metadata-typeFilter/ → type-filter/
- spring-metadata/spring-metadata-condition/ → condition/

### 02-environment-and-properties/property-sources/
- spring-env/spring-env-propertySource/ → property-source/
- spring-env/spring-env-propertySources/ → property-sources/

### 02-environment-and-properties/environment-configuration/
- spring-env/spring-env-environment/ → environment/
- spring-env/spring-env-configurableEnvironment/ → configurable-environment/
- spring-env/spring-env-propertyResolver/ → property-resolver/
- spring-env/spring-env-configurablePropertyResolver/ → configurable-property-resolver/

### 03-data-operations/validation-and-conversion/
- spring-dataops/spring-dataops-validator/ → validator/
- spring-dataops/spring-dataops-propertyEditor/ → property-editor/
- spring-dataops/spring-dataops-converter/ → converter/
- spring-dataops/spring-dataops-converterFactory/ → converter-factory/
- spring-dataops/spring-dataops-genericConverter/ → generic-converter/
- spring-dataops/spring-dataops-conditionalConverter/ → conditional-converter/
- spring-dataops/spring-dataops-conversionService/ → conversion-service/
- spring-dataops/spring-dataops-printer/ → printer/
- spring-dataops/spring-dataops-parser/ → parser/

### 03-data-operations/spel-expressions/
- spring-spel/spring-spel-expressionParser/ → expression-parser/
- spring-spel/spring-spel-expression/ → expression/
- spring-spel/spring-spel-evaluationContext/ → evaluation-context/
- spring-spel/spring-spel-propertyAccessor/ → property-accessor/
- spring-spel/spring-spel-constructorResolver/ → constructor-resolver/
- spring-spel/spring-spel-methodResolver/ → method-resolver/
- spring-spel/spring-spel-beanResolver/ → bean-resolver/
- spring-spel/spring-spel-typeLocator/ → type-locator/
- spring-spel/spring-spel-typeConverter/ → type-converter/
- spring-spel/spring-spel-typeComparator/ → type-comparator/
- spring-spel/spring-spel-operatorOverloader/ → operator-overloader/

## 02-Core-Container

### 01-bean-definitions/bean-definition-basics/
- spring-beans/spring-bean-beanDefinition/ → bean-definition/
- spring-beans/spring-bean-beanDefinitionHolder/ → bean-definition-holder/
- spring-beans/spring-bean-beanDefinitionRegistry/ → bean-definition-registry/

### 01-bean-definitions/bean-readers-and-scanners/
- spring-beans/spring-bean-xmlBeanDefinitionReader/ → xml-bean-definition-reader/
- spring-beans/spring-bean-propertiesBeanDefinitionReader/ → properties-bean-definition-reader/
- spring-beans/spring-bean-groovyBeanDefinitionReader/ → groovy-bean-definition-reader/
- spring-beans/spring-bean-annotatedBeanDefinitionReader/ → annotated-bean-definition-reader/
- spring-beans/spring-bean-classPathBeanDefinitionScanner/ → classpath-bean-definition-scanner/

### 02-bean-factories/factory-interfaces/
- spring-factory/spring-factory-beanFactory/ → bean-factory/
- spring-factory/spring-factory-listableBeanFactory/ → listable-bean-factory/
- spring-factory/spring-factory-hierarchicalBeanFactory/ → hierarchical-bean-factory/
- spring-factory/spring-factory-configurableBeanFactory/ → configurable-bean-factory/
- spring-factory/spring-factory-autowireCapableBeanFactory/ → autowire-capable-bean-factory/
- spring-factory/spring-factory-configurableListableBeanFactory/ → configurable-listable-bean-factory/

### 03-application-contexts/context-implementations/
- spring-context/spring-context-classPathXmlApplicationContext/ → classpath-xml-application-context/
- spring-context/spring-context-annotationConfigApplicationContext/ → annotation-config-application-context/
- spring-context/spring-context-genericApplicationContext/ → generic-application-context/

### 04-bean-lifecycle/registration-process/
- spring-core/spring-core-registerBeanDefinition/ → register-bean-definition/

### 04-bean-lifecycle/initialization-process/
- spring-core/spring-core-getBean/ → get-bean/

### 04-bean-lifecycle/dependency-resolution/
- spring-core/spring-core-resolveDependency/ → resolve-dependency/

### 04-bean-lifecycle/destruction-process/
- spring-core/spring-core-destroyBean/ → destroy-bean/

## 03-Configuration

### 01-annotation-based/core-annotations/
- spring-annotation/spring-annotation-configuration/ → configuration/
- spring-annotation/spring-annotation-bean/ → bean/
- spring-annotation/spring-annotation-import/ → import/
- spring-annotation/spring-annotation-propertySource/ → property-source/
- spring-annotation/spring-annotation-dependsOn/ → depends-on/
- spring-annotation/spring-annotation-conditional/ → conditional/
- spring-annotation/spring-annotation-lazy/ → lazy/
- spring-annotation/spring-annotation-value/ → value/
- spring-annotation/spring-annotation-autowired/ → autowired/

### 01-annotation-based/component-scanning/
- spring-annotation/spring-annotation-componentScan/ → component-scan/
- spring-annotation/spring-annotation-profile/ → profile/

### 02-jsr-standards/jsr-330-dependency-injection/
- spring-jsr/spring-jsr330-inject/ → inject/
- spring-jsr/spring-jsr330-named/ → named/
- spring-jsr/spring-jsr330-qualifier/ → qualifier/
- spring-jsr/spring-jsr330-scope/ → scope/
- spring-jsr/spring-jsr330-singleton/ → singleton/
- spring-jsr/spring-jsr330-provider/ → provider/

### 02-jsr-standards/jsr-250-lifecycle/
- spring-jsr/spring-jsr250-postConstruct/ → post-construct/
- spring-jsr/spring-jsr250-preDestroy/ → pre-destroy/
- spring-jsr/spring-jsr250-resource/ → resource/

## 04-Extension-Points

### 01-aware-interfaces/context-aware/
- spring-aware/spring-aware-applicationContextAware/ → application-context-aware/
- spring-aware/spring-aware-applicationEventPublisherAware/ → application-event-publisher-aware/
- spring-aware/spring-aware-applicationStartupAware/ → application-startup-aware/
- spring-aware/spring-aware-messageSourceAware/ → message-source-aware/
- spring-aware/spring-aware-environmentAware/ → environment-aware/
- spring-aware/spring-aware-importAware/ → import-aware/

### 01-aware-interfaces/resource-aware/
- spring-aware/spring-aware-beanNameAware/ → bean-name-aware/
- spring-aware/spring-aware-beanClassLoaderAware/ → bean-class-loader-aware/
- spring-aware/spring-aware-beanFactoryAware/ → bean-factory-aware/
- spring-aware/spring-aware-embeddedValueResolverAware/ → embedded-value-resolver-aware/
- spring-aware/spring-aware-resourceLoaderAware/ → resource-loader-aware/

### 02-lifecycle-interfaces/initialization-interfaces/
- spring-interface/spring-interface-initializingBean/ → initializing-bean/
- spring-interface/spring-interface-disposableBean/ → disposable-bean/
- spring-interface/spring-interface-smartInitializingSingleton/ → smart-initializing-singleton/

### 02-lifecycle-interfaces/post-processors/
- spring-interface/spring-interface-beanDefinitionRegistryPostProcessor/ → bean-definition-registry-post-processor/
- spring-interface/spring-interface-beanFactoryPostProcessor/ → bean-factory-post-processor/
- spring-interface/spring-interface-beanPostProcessor/ → bean-post-processor/
- spring-interface/spring-interface-instantiationAwareBeanPostProcessor/ → instantiation-aware-bean-post-processor/
- spring-interface/spring-interface-destructionAwareBeanPostProcessor/ → destruction-aware-bean-post-processor/
- spring-interface/spring-interface-mergedBeanDefinitionPostProcessor/ → merged-bean-definition-post-processor/
- spring-interface/spring-interface-smartInstantiationAwareBeanPostProcessor/ → smart-instantiation-aware-bean-post-processor/
