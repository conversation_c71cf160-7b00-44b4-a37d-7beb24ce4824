# 02-Core-Container (核心容器篇)

## 概述

核心容器篇是Spring框架的心脏部分，涵盖了IoC（控制反转）容器和DI（依赖注入）的核心概念。本部分深入探讨Bean的定义、工厂、应用上下文以及完整的Bean生命周期管理。

## 学习目标

- 深入理解IoC容器的工作原理
- 掌握Bean定义和注册机制
- 熟悉各种BeanFactory接口和实现
- 理解ApplicationContext的层次结构
- 掌握Bean的完整生命周期

## 学习路径

### 📁 01-bean-definitions (Bean定义)

#### 🏗️ bean-definition-basics (Bean定义基础)
- **[BeanDefinition](bean-definitions/bean-definition-basics/bean-definition/)** - 详细描述Bean，支持依赖注入、AOP、作用域控制等核心功能
- **[BeanDefinitionHolder](bean-definitions/bean-definition-basics/bean-definition-holder/)** - 管理和操作BeanDefinition的关键类
- **[BeanDefinitionRegistry](bean-definitions/bean-definition-basics/bean-definition-registry/)** - Bean定义注册管理关键接口，处理Bean元数据

#### 📖 bean-readers-and-scanners (Bean读取器与扫描器)
- **[XmlBeanDefinitionReader](bean-definitions/bean-readers-and-scanners/xml-bean-definition-reader/)** - 加载解析XML配置，构建IoC容器，注册Bean定义
- **[PropertiesBeanDefinitionReader](bean-definitions/bean-readers-and-scanners/properties-bean-definition-reader/)** - 属性文件加载，解析为Bean定义
- **[GroovyBeanDefinitionReader](bean-definitions/bean-readers-and-scanners/groovy-bean-definition-reader/)** - Groovy脚本解析为Bean定义
- **[AnnotatedBeanDefinitionReader](bean-definitions/bean-readers-and-scanners/annotated-bean-definition-reader/)** - 注解配置，自动扫描注册Spring组件，简化Bean定义配置
- **[ClassPathBeanDefinitionScanner](bean-definitions/bean-readers-and-scanners/classpath-bean-definition-scanner/)** - 类路径扫描注册Spring Bean，支持自动装配

### 📁 02-bean-factories (Bean工厂)

#### 🔧 factory-interfaces (工厂接口)
- **[BeanFactory](bean-factories/factory-interfaces/bean-factory/)** - Spring的核心接口，提供对Bean的配置、创建、管理的基本功能
- **[ListableBeanFactory](bean-factories/factory-interfaces/listable-bean-factory/)** - 支持按类型获取Bean的集合
- **[HierarchicalBeanFactory](bean-factories/factory-interfaces/hierarchical-bean-factory/)** - 支持父子容器关系，实现Bean定义的层次结构
- **[ConfigurableBeanFactory](bean-factories/factory-interfaces/configurable-bean-factory/)** - 提供对BeanFactory配置的扩展，如属性编辑器、作用域等
- **[AutowireCapableBeanFactory](bean-factories/factory-interfaces/autowire-capable-bean-factory/)** - Bean创建、初始化、注入、销毁的核心功能接口
- **[ConfigurableListableBeanFactory](bean-factories/factory-interfaces/configurable-listable-bean-factory/)** - 支持配置和列表操作的可配置Bean工厂接口

### 📁 03-application-contexts (应用上下文)

#### 🏛️ context-implementations (上下文实现)
- **[ClassPathXmlApplicationContext](application-contexts/context-implementations/classpath-xml-application-context/)** - 类路径（classpath）加载XML配置文件的上下文
- **[AnnotationConfigApplicationContext](application-contexts/context-implementations/annotation-config-application-context/)** - 注解配置类中加载配置信息的上下文
- **[GenericApplicationContext](application-contexts/context-implementations/generic-application-context/)** - 支持多种配置方式，XML、注解、手动注册的上下文

### 📁 04-bean-lifecycle (Bean生命周期)

#### 📝 registration-process (注册过程)
- **[RegisterBeanDefinition](bean-lifecycle/registration-process/register-bean-definition/)** - 加载与解析配置文件，注册解析Bean定义，类名、作用域、属性等

#### 🚀 initialization-process (初始化过程)
- **[GetBean](bean-lifecycle/initialization-process/get-bean/)** - 实例化、属性注入、Aware回调、后置处理器、初始化方法调用

#### 🔗 dependency-resolution (依赖解析)
- **[ResolveDependency](bean-lifecycle/dependency-resolution/resolve-dependency/)** - 声明依赖，查找依赖，注入依赖，处理循环依赖，延迟依赖解析

#### 💥 destruction-process (销毁过程)
- **[DestroyBean](bean-lifecycle/destruction-process/destroy-bean/)** - 销毁方法调用，接口回调，后处理清理，通知触发，GC回收资源

## 核心概念

### IoC容器层次结构
```
ApplicationContext (应用上下文)
    ↓ extends
ConfigurableApplicationContext (可配置应用上下文)
    ↓ extends  
AbstractApplicationContext (抽象应用上下文)
    ↓ contains
BeanFactory (Bean工厂)
```

### Bean生命周期流程
1. **定义阶段** - BeanDefinition创建和注册
2. **实例化阶段** - Bean对象创建
3. **属性注入阶段** - 依赖注入和属性设置
4. **初始化阶段** - Aware接口回调、初始化方法调用
5. **使用阶段** - Bean正常使用
6. **销毁阶段** - 销毁方法调用和资源清理

## 前置知识

- [01-Foundation (基础篇)](../01-Foundation/) - 资源加载和元数据处理
- Java反射机制
- 设计模式：工厂模式、单例模式

## 下一步

完成核心容器篇学习后，建议继续学习：
- [03-Configuration (配置篇)](../03-Configuration/) - 学习不同的配置方式
