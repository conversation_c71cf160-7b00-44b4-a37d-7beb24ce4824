# Spring Framework Learning Project - Reorganization Plan

## Current Structure Analysis

The repository currently has a module-based structure with 14 main directories:
- spring-annotation (11 subdirectories)
- spring-aop (42 subdirectories) 
- spring-aware (11 subdirectories)
- spring-beans (8 subdirectories)
- spring-context (3 subdirectories)
- spring-core (4 subdirectories)
- spring-dataops (9 subdirectories)
- spring-env (6 subdirectories)
- spring-factory (6 subdirectories)
- spring-interface (10 subdirectories)
- spring-jsr (9 subdirectories)
- spring-metadata (4 subdirectories)
- spring-resources (4 subdirectories)
- spring-spel (11 subdirectories)
- spring-transaction (12 subdirectories)
- spring-mvc (1 subdirectory)

## Proposed Learning-Oriented Structure

### 01-Foundation (基础篇)
**Purpose**: Essential concepts and building blocks
- 01-resources-and-metadata/
  - resource-loading/ (from spring-resources)
  - metadata-processing/ (from spring-metadata)
- 02-environment-and-properties/
  - property-sources/ (from spring-env)
  - environment-configuration/ (from spring-env)
- 03-data-operations/
  - validation-and-conversion/ (from spring-dataops)
  - spel-expressions/ (from spring-spel)

### 02-Core-Container (核心容器篇)
**Purpose**: IoC container and dependency injection
- 01-bean-definitions/
  - bean-definition-basics/ (from spring-beans)
  - bean-readers-and-scanners/ (from spring-beans)
- 02-bean-factories/
  - factory-interfaces/ (from spring-factory)
  - factory-implementations/ (from spring-factory)
- 03-application-contexts/
  - context-implementations/ (from spring-context)
  - context-lifecycle/ (from spring-context)
- 04-bean-lifecycle/
  - registration-process/ (from spring-core)
  - initialization-process/ (from spring-core)
  - dependency-resolution/ (from spring-core)
  - destruction-process/ (from spring-core)

### 03-Configuration (配置篇)
**Purpose**: Different ways to configure Spring
- 01-annotation-based/
  - core-annotations/ (from spring-annotation)
  - component-scanning/ (from spring-annotation)
- 02-jsr-standards/
  - jsr-330-dependency-injection/ (from spring-jsr)
  - jsr-250-lifecycle/ (from spring-jsr)

### 04-Extension-Points (扩展点篇)
**Purpose**: Hooks and extension mechanisms
- 01-aware-interfaces/
  - context-aware/ (from spring-aware)
  - resource-aware/ (from spring-aware)
- 02-lifecycle-interfaces/
  - initialization-interfaces/ (from spring-interface)
  - post-processors/ (from spring-interface)

### 05-Aspect-Oriented-Programming (AOP篇)
**Purpose**: Cross-cutting concerns and proxies
- 01-proxy-fundamentals/
  - jdk-dynamic-proxy/ (from spring-aop)
  - cglib-proxy/ (from spring-aop)
- 02-aop-concepts/
  - pointcuts-and-advice/ (from spring-aop)
  - advisors-and-aspects/ (from spring-aop)
- 03-aop-infrastructure/
  - proxy-creation/ (from spring-aop)
  - aspectj-integration/ (from spring-aop)

### 06-Transaction-Management (事务管理篇)
**Purpose**: Declarative and programmatic transactions
- 01-transaction-basics/
  - jdbc-fundamentals/ (from spring-transaction)
  - transaction-definitions/ (from spring-transaction)
- 02-transaction-management/
  - platform-managers/ (from spring-transaction)
  - declarative-transactions/ (from spring-transaction)

### 07-Web-Framework (Web框架篇)
**Purpose**: Spring MVC and web-related features
- 01-spring-mvc/
  - mvc-basics/ (from spring-mvc)
  - web-configuration/ (from spring-mvc)

## Directory Mapping Strategy

Each new directory will contain:
1. **README.md** - Overview and learning objectives
2. **concepts/** - Theoretical explanations
3. **examples/** - Practical code examples
4. **exercises/** - Hands-on practice
5. **references/** - Links to related topics

## Benefits of New Structure

1. **Progressive Learning**: Follows natural learning progression
2. **Logical Grouping**: Related concepts are grouped together
3. **Clear Dependencies**: Prerequisites are clearly defined
4. **Easier Navigation**: Intuitive folder names and structure
5. **Better Discoverability**: Topics are easier to find
