# 03-Configuration (配置篇)

## 概述

配置篇介绍了Spring框架中不同的配置方式，包括基于注解的配置和JSR标准规范。本部分帮助你掌握现代Spring应用的配置最佳实践。

## 学习目标

- 掌握Spring核心注解的使用
- 理解组件扫描机制
- 熟悉JSR-330和JSR-250标准
- 学会选择合适的配置方式

## 学习路径

### 📁 01-annotation-based (基于注解)

#### 🏷️ core-annotations (核心注解)
- **[@Configuration](annotation-based/core-annotations/configuration/)** - 声明类为配置类，定义Bean和Bean之间的依赖关系
- **[@Bean](annotation-based/core-annotations/bean/)** - 在配置类中声明方法，返回Bean实例
- **[@Import](annotation-based/core-annotations/import/)** - 引入其他配置类，将其Bean定义合并到当前容器
- **[@PropertySource](annotation-based/core-annotations/property-source/)** - 指定属性文件，加载外部配置到环境中
- **[@DependsOn](annotation-based/core-annotations/depends-on/)** - 指定Bean的依赖顺序，确保特定Bean在其他Bean之前初始化
- **[@Conditional](annotation-based/core-annotations/conditional/)** - 根据条件决定是否创建Bean
- **[@Lazy](annotation-based/core-annotations/lazy/)** - 指定Bean的延迟初始化，只有在首次使用时才创建
- **[@Value](annotation-based/core-annotations/value/)** - 注入简单值或表达式到Bean的字段或方法参数
- **[@Autowired](annotation-based/core-annotations/autowired/)** - 自动装配Bean依赖

#### 🔍 component-scanning (组件扫描)
- **[@ComponentScan](annotation-based/component-scanning/component-scan/)** - 启用组件扫描，自动发现并注册标记为组件的类
- **[@Profile](annotation-based/component-scanning/profile/)** - 根据环境配置激活不同的Bean配置

### 📁 02-jsr-standards (JSR标准)

#### 💉 jsr-330-dependency-injection (JSR-330依赖注入)
- **[@Inject](jsr-standards/jsr-330-dependency-injection/inject/)** - JSR-330标准的依赖注入注解
- **[@Named](jsr-standards/jsr-330-dependency-injection/named/)** - JSR-330标准的命名注解
- **[@Qualifier](jsr-standards/jsr-330-dependency-injection/qualifier/)** - 用于限定注入的Bean
- **[@Scope](jsr-standards/jsr-330-dependency-injection/scope/)** - 指定Bean的作用域
- **[@Singleton](jsr-standards/jsr-330-dependency-injection/singleton/)** - 指定Bean为单例
- **[Provider](jsr-standards/jsr-330-dependency-injection/provider/)** - Java标准库提供的通用Bean工厂接口

#### 🔄 jsr-250-lifecycle (JSR-250生命周期)
- **[@PostConstruct](jsr-standards/jsr-250-lifecycle/post-construct/)** - 指定初始化方法
- **[@PreDestroy](jsr-standards/jsr-250-lifecycle/pre-destroy/)** - 指定销毁方法
- **[@Resource](jsr-standards/jsr-250-lifecycle/resource/)** - Java EE标准的资源注入注解

## 配置方式对比

### XML配置 vs 注解配置

| 特性 | XML配置 | 注解配置 |
|------|---------|----------|
| 配置位置 | 外部XML文件 | Java类中 |
| 类型安全 | 编译时不检查 | 编译时检查 |
| 重构友好 | 不友好 | 友好 |
| 配置集中性 | 集中 | 分散 |
| 学习曲线 | 较陡 | 较平 |

### Spring注解 vs JSR标准

| 功能 | Spring注解 | JSR标准 |
|------|------------|---------|
| 依赖注入 | @Autowired | @Inject |
| Bean命名 | @Component("name") | @Named("name") |
| 限定符 | @Qualifier | @Qualifier |
| 初始化 | @PostConstruct | @PostConstruct |
| 销毁 | @PreDestroy | @PreDestroy |
| 资源注入 | @Autowired | @Resource |

## 最佳实践

### 1. 配置类组织
```java
@Configuration
@ComponentScan(basePackages = "com.example")
@PropertySource("classpath:application.properties")
public class AppConfig {
    // Bean定义
}
```

### 2. 条件化配置
```java
@Configuration
@Profile("production")
public class ProductionConfig {
    // 生产环境配置
}
```

### 3. 模块化配置
```java
@Configuration
@Import({DatabaseConfig.class, SecurityConfig.class})
public class MainConfig {
    // 主配置
}
```

## 注解处理流程

1. **扫描阶段** - ClassPathBeanDefinitionScanner扫描指定包
2. **解析阶段** - AnnotationMetadata解析注解信息
3. **注册阶段** - BeanDefinitionRegistry注册Bean定义
4. **处理阶段** - ConfigurationClassPostProcessor处理配置类
5. **实例化阶段** - 根据注解信息创建Bean实例

## 前置知识

- [02-Core-Container (核心容器篇)](../02-Core-Container/) - IoC容器和Bean生命周期
- Java注解机制
- 反射和代理模式

## 下一步

完成配置篇学习后，建议继续学习：
- [04-Extension-Points (扩展点篇)](../04-Extension-Points/) - 学习Spring的扩展机制
