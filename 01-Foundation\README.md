# 01-Foundation (基础篇)

## 概述

基础篇涵盖了Spring框架的核心基础概念，这些是理解Spring框架工作原理的必备知识。本部分包含资源加载、元数据处理、环境配置和数据操作等基础功能。

## 学习目标

- 理解Spring的资源加载机制
- 掌握元数据处理和类型过滤
- 学会环境配置和属性管理
- 熟悉数据验证、转换和SpEL表达式

## 学习路径

### 📁 01-resources-and-metadata (资源与元数据)

#### 🔧 resource-loading (资源加载)
- **[Resource](resource-loading/resource/)** - 抽象接口，表示文件、类路径等，用于访问不同来源的资源
- **[ResourceLoader](resource-loading/resource-loader/)** - 资源获取核心接口，实现统一加载不同位置资源的策略
- **[ResourcePatternResolver](resource-loading/resource-pattern-resolver/)** - 资源模式解析接口，用于灵活加载应用中的多种资源
- **[DocumentLoader](resource-loading/document-loader/)** - XML文档加载解析核心接口，支持后台自动配置Spring应用

#### 🔍 metadata-processing (元数据处理)
- **[MetadataReader](metadata-processing/metadata-reader/)** - 类元数据获取核心，支持组件扫描、条件化注解、AOP等高级功能
- **[AnnotationMetadata](metadata-processing/annotation-metadata/)** - 动态获取和操作运行时类注解信息
- **[TypeFilter](metadata-processing/type-filter/)** - 组件扫描时自定义类筛选，支持复杂条件和精确过滤
- **[Condition](metadata-processing/condition/)** - 条件判断，决定Bean创建和配置的灵活机制

### 📁 02-environment-and-properties (环境与属性)

#### 🏷️ property-sources (属性源)
- **[PropertySource](property-sources/property-source/)** - 管理各种配置源的抽象类，支持灵活地加载和访问应用配置
- **[PropertySources](property-sources/property-sources/)** - 用于统一管理和访问多个PropertySource实例，简化配置数据的处理

#### ⚙️ environment-configuration (环境配置)
- **[Environment](environment-configuration/environment/)** - 应用环境表示，提供属性访问，支持配置文件，实现动态配置
- **[ConfigurableEnvironment](environment-configuration/configurable-environment/)** - 动态配置应用环境，激活、默认配置，提升应用灵活性
- **[PropertyResolver](environment-configuration/property-resolver/)** - 通用属性解析，获取配置值，处理属性缺失，简便灵活
- **[ConfigurablePropertyResolver](environment-configuration/configurable-property-resolver/)** - 属性解析配置，占位符设置，适应不同配置需求

### 📁 03-data-operations (数据操作)

#### ✅ validation-and-conversion (验证与转换)
- **[Validator](validation-and-conversion/validator/)** - 提供自定义数据验证逻辑，确保模型对象满足业务规则
- **[PropertyEditor](validation-and-conversion/property-editor/)** - 自定义JavaBean属性的转换逻辑，处理属性类型转换
- **[Converter](validation-and-conversion/converter/)** - 用于不同类型间的转换，定义简单的源至目标类型转换规则
- **[ConverterFactory](validation-and-conversion/converter-factory/)** - 创建针对特定源类型的转换器，用于类型转换
- **[GenericConverter](validation-and-conversion/generic-converter/)** - 更复杂的转换器，支持多种源和目标类型转换
- **[ConditionalConverter](validation-and-conversion/conditional-converter/)** - 根据条件选择是否执行转换的转换器
- **[ConversionService](validation-and-conversion/conversion-service/)** - 提供统一的类型转换服务接口，管理转换器
- **[Printer](validation-and-conversion/printer/)** - 用于将对象格式化为文本，专注于格式化输出
- **[Parser](validation-and-conversion/parser/)** - 用于将文本解析为对象，专注于解析逻辑

#### 🔤 spel-expressions (SpEL表达式)
- **[ExpressionParser](spel-expressions/expression-parser/)** - 解析字符串形式的SpEL表达式，创建并返回Expression实例
- **[Expression](spel-expressions/expression/)** - 对表达式字符串进行求值的功能，支持类型转换、获取原始字符串等操作
- **[EvaluationContext](spel-expressions/evaluation-context/)** - 管理SpEL表达式的上下文信息
- **[PropertyAccessor](spel-expressions/property-accessor/)** - 用于读取和写入对象的属性，可用于实现自定义的属性访问逻辑
- **[ConstructorResolver](spel-expressions/constructor-resolver/)** - 解析构造函数确定bean的实例化方式
- **[MethodResolver](spel-expressions/method-resolver/)** - 解析类方法，确保正确调用，处理重载和参数匹配
- **[BeanResolver](spel-expressions/bean-resolver/)** - 解析bean定义，包括依赖、属性设置，实例化并返回
- **[TypeLocator](spel-expressions/type-locator/)** - 动态查找类，返回Class对象，在表达式解析、类型转换等
- **[TypeConverter](spel-expressions/type-converter/)** - 类型转换功能，将表达式中的数据从一种类型转换为另一种类型
- **[TypeComparator](spel-expressions/type-comparator/)** - 类型比较功能，定义了比较两个对象是否相等的方法
- **[OperatorOverloader](spel-expressions/operator-overloader/)** - 运算符重载功能，对表达式中的运算符进行自定义操作的方法

## 前置知识

- Java基础语法
- 面向对象编程概念
- 基本的设计模式知识

## 下一步

完成基础篇学习后，建议继续学习：
- [02-Core-Container (核心容器篇)](../02-Core-Container/) - 深入理解IoC容器和依赖注入
