<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-reading</artifactId>
        <groupId>com.xcs.spring</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>spring-aop-advisor</module>
        <module>spring-aop-pointcut</module>
        <module>spring-aop-advisorAdapter</module>
        <module>spring-aop-targetSource</module>
        <module>spring-aop-aopProxy</module>
        <module>spring-aop-classFilter</module>
        <module>spring-aop-methodMatcher</module>
        <module>spring-aop-jdkProxy</module>
        <module>spring-aop-cglibProxy</module>
        <module>spring-aop-enableAspectJAutoProxy</module>
        <module>spring-aop-enableLoadTimeWeaving</module>
        <module>spring-aop-advice-methodInterceptor</module>
        <module>spring-aop-advice-methodBeforeAdvice</module>
        <module>spring-aop-advice-afterReturningAdvice</module>
        <module>spring-aop-advice-throwsAdvice</module>
        <module>spring-aop-advice-introductionInterceptor</module>
        <module>spring-aop-aopProxyFactory</module>
        <module>spring-aop-annotationAwareAspectJAutoProxyCreator</module>
        <module>spring-aop-aspectInstanceFactory</module>
        <module>spring-aop-metadataAwareAspectInstanceFactory</module>
        <module>spring-aop-aspectJAdvisorFactory</module>
        <module>spring-aop-beanFactoryAspectJAdvisorsBuilder</module>
        <module>spring-aop-beanFactoryAdvisorRetrievalHelper</module>
        <module>spring-aop-proxyFactory</module>
        <module>spring-aop-advisorChainFactory</module>
        <module>spring-aop-advisorAdapterRegistry</module>
        <module>spring-aop-advised</module>
        <module>spring-aop-aopContext</module>
        <module>spring-aop-targetSourceCreator</module>
        <module>spring-aop-exposeInvocationInterceptor</module>
        <module>spring-aop-advice</module>
        <module>spring-aop-proxyMethodInvocation</module>
    </modules>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-aop</artifactId>

</project>