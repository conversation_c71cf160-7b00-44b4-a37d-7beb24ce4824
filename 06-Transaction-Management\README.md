# 06-Transaction-Management (事务管理篇)

## 概述

事务管理篇深入探讨Spring框架的事务管理机制，包括编程式事务和声明式事务。Spring的事务管理是企业级应用开发中的核心功能，确保数据的一致性和完整性。

## 学习目标

- 理解事务的ACID特性和隔离级别
- 掌握JDBC基础和数据源配置
- 熟悉Spring事务管理的核心接口
- 学会使用声明式事务管理
- 理解事务传播行为和回滚规则

## 学习路径

### 📁 01-transaction-basics (事务基础)

#### 🔌 jdbc-fundamentals (JDBC基础)
- **[Connection](transaction-basics/jdbc-fundamentals/connection/)** - 管理数据库连接，执行SQL，处理事务
- **[DataSource](transaction-basics/jdbc-fundamentals/data-source/)** - 提供高效管理数据库连接的接口
- **[DriverManager](transaction-basics/jdbc-fundamentals/driver-manager/)** - 管理和建立数据库连接的核心类
- **[JdbcTemplate](transaction-basics/jdbc-fundamentals/jdbc-template/)** - 简化了JDBC操作，提供了方便的数据库访问抽象

#### 📋 transaction-definitions (事务定义)
- **[TransactionDefinition](transaction-basics/transaction-definitions/transaction-definition/)** - 定义事务的传播行为和隔离级别
- **[TransactionAttributeSource](transaction-basics/transaction-definitions/transaction-attribute-source/)** - 用于获取事务属性的策略接口

### 📁 02-transaction-management (事务管理)

#### 🏭 platform-managers (平台管理器)
- **[PlatformTransactionManager](transaction-management/platform-managers/platform-transaction-manager/)** - 用于管理和协调事务的生命周期和执行
- **[TransactionTemplate](transaction-management/platform-managers/transaction-template/)** - 简化事务管理，支持编程式事务控制与异常处理

#### 🎯 declarative-transactions (声明式事务)
- **[@EnableTransactionManagement](transaction-management/declarative-transactions/enable-transaction-management/)** - 启用Spring的注解驱动事务管理
- **[SpringTransactionAnnotationParser](transaction-management/declarative-transactions/spring-transaction-annotation-parser/)** - 解析@Transactional注解并转换为事务配置
- **[TransactionInterceptor](transaction-management/declarative-transactions/transaction-interceptor/)** - 事务拦截器，用于管理方法级别的事务处理

## 事务核心概念

### ACID特性

| 特性 | 英文 | 说明 |
|------|------|------|
| 原子性 | Atomicity | 事务是不可分割的工作单位 |
| 一致性 | Consistency | 事务前后数据的完整性必须保持一致 |
| 隔离性 | Isolation | 多个事务并发执行时相互不影响 |
| 持久性 | Durability | 事务一旦提交，对数据的改变是永久的 |

### 隔离级别

| 级别 | 说明 | 脏读 | 不可重复读 | 幻读 |
|------|------|------|------------|------|
| READ_UNCOMMITTED | 读未提交 | ✓ | ✓ | ✓ |
| READ_COMMITTED | 读已提交 | ✗ | ✓ | ✓ |
| REPEATABLE_READ | 可重复读 | ✗ | ✗ | ✓ |
| SERIALIZABLE | 串行化 | ✗ | ✗ | ✗ |

### 传播行为

| 传播行为 | 说明 |
|----------|------|
| REQUIRED | 如果当前存在事务，则加入该事务；如果不存在，则创建新事务 |
| SUPPORTS | 如果当前存在事务，则加入该事务；如果不存在，则以非事务方式执行 |
| MANDATORY | 如果当前存在事务，则加入该事务；如果不存在，则抛出异常 |
| REQUIRES_NEW | 创建新事务，如果当前存在事务，则挂起当前事务 |
| NOT_SUPPORTED | 以非事务方式执行，如果当前存在事务，则挂起当前事务 |
| NEVER | 以非事务方式执行，如果当前存在事务，则抛出异常 |
| NESTED | 如果当前存在事务，则在嵌套事务内执行 |

## 事务管理方式

### 1. 编程式事务管理

#### 使用TransactionTemplate
```java
@Service
public class UserService {
    @Autowired
    private TransactionTemplate transactionTemplate;
    
    public void createUser(User user) {
        transactionTemplate.execute(status -> {
            // 事务逻辑
            userDao.save(user);
            return null;
        });
    }
}
```

#### 使用PlatformTransactionManager
```java
@Service
public class UserService {
    @Autowired
    private PlatformTransactionManager transactionManager;
    
    public void createUser(User user) {
        TransactionStatus status = transactionManager.getTransaction(
            new DefaultTransactionDefinition());
        try {
            userDao.save(user);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }
}
```

### 2. 声明式事务管理

#### 基于注解
```java
@Service
@Transactional
public class UserService {
    
    @Transactional(propagation = Propagation.REQUIRED, 
                   isolation = Isolation.READ_COMMITTED,
                   rollbackFor = Exception.class)
    public void createUser(User user) {
        userDao.save(user);
    }
    
    @Transactional(readOnly = true)
    public User findUser(Long id) {
        return userDao.findById(id);
    }
}
```

#### 基于XML配置
```xml
<tx:advice id="txAdvice" transaction-manager="transactionManager">
    <tx:attributes>
        <tx:method name="create*" propagation="REQUIRED"/>
        <tx:method name="update*" propagation="REQUIRED"/>
        <tx:method name="delete*" propagation="REQUIRED"/>
        <tx:method name="find*" read-only="true"/>
    </tx:attributes>
</tx:advice>

<aop:config>
    <aop:pointcut id="serviceOperation" 
                  expression="execution(* com.example.service.*.*(..))"/>
    <aop:advisor advice-ref="txAdvice" pointcut-ref="serviceOperation"/>
</aop:config>
```

## 事务实现原理

### 1. 声明式事务实现流程
```
1. @EnableTransactionManagement启用事务管理
2. TransactionInterceptor拦截方法调用
3. 解析@Transactional注解获取事务属性
4. PlatformTransactionManager管理事务
5. 根据结果提交或回滚事务
```

### 2. 事务同步机制
```
1. TransactionSynchronizationManager管理事务资源
2. 将Connection绑定到当前线程
3. 事务提交前执行同步回调
4. 事务完成后清理资源
```

### 3. 事务传播实现
```
1. 检查当前线程是否存在事务
2. 根据传播行为决定事务策略
3. 创建新事务或加入现有事务
4. 设置事务属性和同步状态
```

## 配置示例

### 1. 数据源配置
```java
@Configuration
@EnableTransactionManagement
public class TransactionConfig {
    
    @Bean
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("********************************");
        dataSource.setUsername("root");
        dataSource.setPassword("password");
        return dataSource;
    }
    
    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
    
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
```

### 2. 多数据源事务配置
```java
@Configuration
@EnableTransactionManagement
public class MultiDataSourceConfig {
    
    @Bean
    @Primary
    public PlatformTransactionManager primaryTransactionManager(
            @Qualifier("primaryDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
    
    @Bean
    public PlatformTransactionManager secondaryTransactionManager(
            @Qualifier("secondaryDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
```

## 最佳实践

### 1. 事务边界设计
- 事务应该尽可能短
- 避免在事务中执行长时间操作
- 合理设置事务超时时间

### 2. 异常处理
- 明确指定回滚异常类型
- 区分业务异常和系统异常
- 合理使用检查异常和运行时异常

### 3. 性能优化
- 使用只读事务优化查询操作
- 合理选择隔离级别
- 避免不必要的事务嵌套

### 4. 常见陷阱
- 同一类内方法调用事务失效
- private方法上的@Transactional无效
- 异常被捕获导致事务不回滚

## 前置知识

- [05-Aspect-Oriented-Programming (AOP篇)](../05-Aspect-Oriented-Programming/) - AOP代理机制
- JDBC基础知识
- 数据库事务概念
- SQL基础语法

## 下一步

完成事务管理篇学习后，建议继续学习：
- [07-Web-Framework (Web框架篇)](../07-Web-Framework/) - Spring MVC框架
